{"absolute_value": 0, "align_labels_right": 0, "creation": "2025-09-26 11:22:32.323123", "css": "/* DPR Custom Print Format Styles */\n\n/* Reset and Base Styles */\n* {\n    margin: 0;\n    padding: 0;\n    box-sizing: border-box;\n}\n\nbody {\n    font-family: Arial, sans-serif;\n    font-size: 12px;\n    line-height: 1.4;\n    color: #000;\n    background: #fff;\n    padding: 2px;\n}\n\n/* Header Styles */\n.header {\n    text-align: center;\n    font-weight: bold;\n    font-size: 16px;\n    margin-bottom: 20px;\n    text-decoration: underline;\n    letter-spacing: 1px;\n}\n\n/* Information Section */\n.info-section {\n    margin-bottom: 20px;\n}\n\n.info-row {\n    display: flex;\n    margin-bottom: 5px;\n    align-items: center;\n}\n\n.info-label {\n    font-weight: bold;\n    width: 120px;\n    flex-shrink: 0;\n}\n\n.date-info {\n    margin-left: auto;\n    font-weight: bold;\n}\n\n/* Section Titles */\n.section-title {\n    font-weight: bold;\n    text-align: center;\n    margin: 20px 0 10px 0;\n    text-decoration: underline;\n    font-size: 14px;\n}\n\n.subsection-title {\n    font-weight: bold;\n    text-decoration: underline;\n    text-align: center;\n    margin-bottom: 10px;\n    font-size: 12px;\n}\n\n/* Table Styles */\ntable {\n    width: 100%;\n    border-collapse: collapse;\n    margin-bottom: 20px;\n    font-size: 11px;\n}\n\nth, td {\n    border: 1px solid #000;\n    padding: 4px 6px;\n    text-align: center;\n    vertical-align: middle;\n}\n\nth {\n    background-color: #f0f0f0;\n    font-weight: bold;\n    font-size: 10px;\n}\n\n.text-left {\n    text-align: left !important;\n}\n\n/* Manpower Table */\n.manpower-table th:first-child,\n.manpower-table td:first-child {\n    width: 5%;\n}\n\n.manpower-table th:nth-child(2),\n.manpower-table td:nth-child(2) {\n    width: 25%;\n}\n\n.manpower-table th:nth-child(n+3),\n.manpower-table td:nth-child(n+3) {\n    width: 8.75%;\n}\n\n.total-row {\n    font-weight: bold;\n    background-color: #f8f8f8;\n}\n\n/* Work Status */\n.work-status-container {\n    display: flex;\n    gap: 20px;\n    margin-bottom: 20px;\n}\n\n.work-status-left,\n.work-status-right {\n    flex: 1;\n}\n\n.work-status-table {\n    width: 100%;\n}\n\n.work-status-table th:first-child,\n.work-status-table td:first-child {\n    width: 10%;\n}\n\n.work-status-table th:nth-child(2),\n.work-status-table td:nth-child(2) {\n    width: 90%;\n}\n\n/* Hold Ups and Request Sections */\n.hold-ups-section,\n.request-info-section {\n    margin: 20px 0;\n}\n\n.hold-ups-section h4,\n.request-info-section h4 {\n    font-weight: bold;\n    text-decoration: underline;\n    text-align: center;\n    margin-bottom: 10px;\n    font-size: 12px;\n}\n\n.content-box {\n    min-height: 30px;\n    padding: 5px;\n    border: 1px solid #ccc;\n    background-color: #fafafa;\n}\n\n/* Equipment and Material Container */\n.equipment-material-container {\n    display: flex;\n    gap: 20px;\n    margin-bottom: 20px;\n}\n\n.equipment-section,\n.material-section {\n    flex: 1;\n}\n\n/* Equipment Table */\n.equipment-table th:first-child,\n.equipment-table td:first-child {\n    width: 10%;\n}\n\n.equipment-table th:nth-child(2),\n.equipment-table td:nth-child(2) {\n    width: 70%;\n}\n\n.equipment-table th:nth-child(3),\n.equipment-table td:nth-child(3) {\n    width: 20%;\n}\n\n/* Material Table */\n.material-table th:first-child,\n.material-table td:first-child {\n    width: 8%;\n}\n\n.material-table th:nth-child(2),\n.material-table td:nth-child(2) {\n    width: 30%;\n}\n\n.material-table th:nth-child(3),\n.material-table td:nth-child(3) {\n    width: 12%;\n}\n\n.material-table th:nth-child(4),\n.material-table td:nth-child(4) {\n    width: 25%;\n}\n\n.material-table th:nth-child(5),\n.material-table td:nth-child(5) {\n    width: 25%;\n}\n\n/* Material Status Section */\n.material-status-section {\n    margin-top: 20px;\n}\n\n.material-status-section h4 {\n    font-weight: bold;\n    text-decoration: underline;\n    margin-bottom: 10px;\n    font-size: 12px;\n}\n\n.material-status-content {\n    padding: 10px;\n    border: 1px solid #ccc;\n    background-color: #fafafa;\n}\n\n.material-category {\n    margin-bottom: 10px;\n}\n\n.material-category strong {\n    display: block;\n    margin-bottom: 5px;\n    text-decoration: underline;\n}\n\n.material-items {\n    margin-left: 10px;\n    line-height: 1.6;\n}\n\n/* Print Specific Styles */\n@media print {\n    body {\n        padding: 2px;\n        font-size: 11px;\n    }\n    \n    .header {\n        font-size: 14px;\n    }\n    \n    table {\n        font-size: 10px;\n    }\n    \n    th, td {\n        padding: 3px 4px;\n    }\n    \n    .work-status-container,\n    .equipment-material-container {\n        gap: 15px;\n    }\n}\n\n/* Responsive Design for smaller screens */\n@media screen and (max-width: 768px) {\n    .work-status-container,\n    .equipment-material-container {\n        flex-direction: column;\n        gap: 10px;\n    }\n    \n    .info-row {\n        flex-direction: column;\n        align-items: flex-start;\n    }\n    \n    .date-info {\n        margin-left: 0;\n        margin-top: 5px;\n    }\n}\n\n/* Utility Classes */\n.no-border {\n    border: none !important;\n}\n\n.bold {\n    font-weight: bold;\n}\n\n.center {\n    text-align: center;\n}\n\n.underline {\n    text-decoration: underline;\n}\n\n.small-text {\n    font-size: 10px;\n}\n\n.large-text {\n    font-size: 14px;\n}\n", "custom_format": 1, "default_print_language": "en", "disabled": 0, "doc_type": "DPR", "docstatus": 0, "doctype": "Print Format", "font_size": 14, "html": "<!DOCTYPE html>\n<html>\n<head>\n    <meta charset=\"UTF-8\">\n    <title>DPR Custom Print Format</title>\n    <link rel=\"stylesheet\" href=\"dpr_custom_styles.css\">\n</head>\n<body>\n    <!-- Header -->\n    <div class=\"header\">\n        YOGI BUILDCON DAILY PROGRESS REPORT\n    </div>\n    \n    <!-- Basic Information -->\n    <div class=\"info-section\">\n        <div class=\"info-row\">\n            <span class=\"info-label\">Client Name :</span>\n            <span>{{ doc.client }}</span>\n            <span class=\"date-info\">Date : {{ frappe.utils.formatdate(doc.date, 'dd-mm-yyyy') if doc.date else '' }}</span>\n        </div>\n        <div class=\"info-row\">\n            <span class=\"info-label\">Project :</span>\n            <span>{{ doc.project }}</span>\n            <span class=\"date-info\">Total Days : {{ doc.total_days }}</span>\n        </div>\n        <div class=\"info-row\">\n            <span class=\"info-label\">Address :</span>\n            <span>{{ doc.address }}</span>\n            <span class=\"date-info\">Days Elapsed : {{ doc.days_elapsed }}</span>\n        </div>\n        <div class=\"info-row\">\n            <span class=\"info-label\"> </span>\n            <span class=\"date-info\">Balance : {{ doc.balance }}</span>\n        </div>\n    </div>\n    \n    <!-- Manpower Strength Report -->\n    <div class=\"section-title\">Manpower strength report</div>\n    <table class=\"manpower-table\">\n        <thead>\n            <tr>\n                <th>No</th>\n                <th>Description</th>\n                <th>Prev</th>\n                <th>Today</th>\n                <th>Specialized</th>\n                <th>Carp</th>\n                <th>Fitt</th>\n                <th>B. Mas</th>\n                <th>P.Mas</th>\n                <th>M/C</th>\n            </tr>\n        </thead>\n        <tbody>\n            {% for row in doc.manpower_table %}\n            <tr>\n                <td>{{ loop.index }}</td>\n                <td class=\"text-left\">{{ row.description or '' }}</td>\n                <td>{{ row.prev or 0 }}</td>\n                <td>{{ row.today or 0 }}</td>\n                <td>{{ row.specialized or 0 }}</td>\n                <td>{{ row.carp or 0 }}</td>\n                <td>{{ row.fitt or 0 }}</td>\n                <td>{{ row.b_mas or 0 }}</td>\n                <td>{{ row.pmas or 0 }}</td>\n                <td>{{ row.mc or 0 }}</td>\n            </tr>\n            {% endfor %}\n            <tr class=\"total-row\">\n                <td colspan=\"2\">Total</td>\n                <td>{{ doc.total_prev or 0 }}</td>\n                <td>{{ doc.total_today or 0 }}</td>\n                <td>{{ doc.total_spec or 0 }}</td>\n                <td>{{ doc.total_carp or 0 }}</td>\n                <td>{{ doc.total_fitt or 0 }}</td>\n                <td>{{ doc.total_b_mas or 0 }}</td>\n                <td>{{ doc.total_p_mas or 0 }}</td>\n                <td>{{ doc.total_mc or 0 }}</td>\n            </tr>\n        </tbody>\n    </table>\n    \n    <!-- Work Status -->\n    <div class=\"section-title\">Work Status</div>\n    <div class=\"work-status-container\">\n        <div class=\"work-status-left\">\n            <table class=\"work-status-table\">\n                <thead>\n                    <tr>\n                        <th>No</th>\n                        <th>Work Done Today</th>\n                    </tr>\n                </thead>\n                <tbody>\n                    {% for row in doc.wdone_today_table %}\n                    <tr>\n                        <td>{{ loop.index }}</td>\n                        <td class=\"text-left\">{{ row.work_done_today or '' }}</td>\n                    </tr>\n                    {% endfor %}\n                </tbody>\n            </table>\n        </div>\n        <div class=\"work-status-right\">\n            <table class=\"work-status-table\">\n                <thead>\n                    <tr>\n                        <th>No</th>\n                        <th>Work Planned Tomorrow</th>\n                    </tr>\n                </thead>\n                <tbody>\n                    {% for row in doc.wplanned_tomm_table %}\n                    <tr>\n                        <td>{{ loop.index }}</td>\n                        <td class=\"text-left\">{{ row.work_planned_tommorow or '' }}</td>\n                    </tr>\n                    {% endfor %}\n                </tbody>\n            </table>\n        </div>\n    </div>\n    \n    <!-- Hold Ups / Stoppages -->\n    <div class=\"hold-ups-section\">\n        <h4>Hold Ups /Stoppages of works with reasons</h4>\n        <div class=\"content-box\">{{ doc.hold_ups_stoppages_of_works_with_reasons or 'None' }}</div>\n    </div>\n    \n    <!-- Request for Information -->\n    <div class=\"request-info-section\">\n        <h4>Request for information</h4>\n        <div class=\"content-box\">{{ doc.request_for_information or 'None' }}</div>\n    </div>\n    \n    <!-- Equipment and Material Section -->\n    <div class=\"equipment-material-container\">\n        <div class=\"equipment-section\">\n            <h4 class=\"subsection-title\">Equipment's/Machinery Deployed</h4>\n            <table class=\"equipment-table\">\n                <thead>\n                    <tr>\n                        <th>No</th>\n                        <th>Machinery Deployed</th>\n                        <th>No(s)</th>\n                    </tr>\n                </thead>\n                <tbody>\n                    {% for row in doc.mach_deployed_table %}\n                    <tr>\n                        <td>{{ loop.index }}</td>\n                        <td class=\"text-left\">{{ row.machinery_deployed or '' }}</td>\n                        <td>{{ row.nos or '' }}</td>\n                    </tr>\n                    {% endfor %}\n                </tbody>\n            </table>\n        </div>\n        \n        <div class=\"material-section\">\n            <h4 class=\"subsection-title\">Today's Material Receipt</h4>\n            <table class=\"material-table\">\n                <thead>\n                    <tr>\n                        <th>No</th>\n                        <th>Material</th>\n                        <th>Qty</th>\n                        <th>Desc</th>\n                        <th>Remark</th>\n                    </tr>\n                </thead>\n                <tbody>\n                    {% for row in doc.mat_recp_table %}\n                    <tr>\n                        <td>{{ loop.index }}</td>\n                        <td class=\"text-left\">{{ row.material or '' }}</td>\n                        <td>{{ row.qty or '' }}</td>\n                        <td class=\"text-left\">{{ row.desc or '' }}</td>\n                        <td class=\"text-left\">{{ row.remark or '' }}</td>\n                    </tr>\n                    {% endfor %}\n                </tbody>\n            </table>\n        </div>\n    </div>\n    \n    <!-- Status of Material -->\n    <div class=\"material-status-section\">\n        <h4>Status of Material :</h4>\n        <div class=\"material-status-content\">\n            <div class=\"material-category\">\n                <div class=\"material-items\">\n                    {{ doc.status_of_material or '' }}\n                </div>\n            </div>\n        </div>\n    </div>\n    \n</body>\n</html>\n", "idx": 0, "line_breaks": 0, "margin_bottom": 15.0, "margin_left": 15.0, "margin_right": 15.0, "margin_top": 15.0, "modified": "2025-09-26 13:25:03.013188", "modified_by": "Administrator", "module": "Buildo Frappe", "name": "Yogi DPR", "owner": "Administrator", "page_number": "<PERSON>de", "pdf_generator": "chrome", "print_designer": 0, "print_designer_template_app": "print_designer", "print_format_builder": 0, "print_format_builder_beta": 0, "print_format_for": "DocType", "print_format_type": "<PERSON><PERSON>", "raw_printing": 0, "show_section_headings": 0, "standard": "Yes"}